/**
 * Database helper functions with proper TypeScript typing
 * Examples of how to use the generated database types
 */

import { createClient } from './client';
import type {
  Post,
  PostWithRelations,
  InsertPost,
  UpdatePost,
  Profile,
  Category,
  Tag,
  ContentStatus,
  UserRole,
  PaginatedResponse,
  QueryParams,
} from '@/types';

// =============================================================================
// POST OPERATIONS
// =============================================================================

/**
 * Get all posts with optional filtering and pagination
 */
export async function getPosts(params: QueryParams = {}): Promise<PaginatedResponse<PostWithRelations>> {
  const supabase = createClient();
  const {
    page = 1,
    limit = 10,
    search,
    status,
    category,
    author,
    sortBy = 'created_at',
    sortOrder = 'desc',
  } = params;

  let query = supabase
    .from('posts')
    .select(`
      *,
      author:profiles!posts_author_id_fkey(id, full_name, email, avatar_url),
      category:categories!posts_category_id_fkey(id, name, slug),
      last_edited_by:profiles!posts_last_edited_by_id_fkey(id, full_name, email)
    `)
    .is('deleted_at', null);

  // Apply filters
  if (search) {
    query = query.or(`title.ilike.%${search}%, excerpt.ilike.%${search}%`);
  }
  if (status) {
    query = query.eq('status', status as ContentStatus);
  }
  if (category) {
    query = query.eq('category_id', category);
  }
  if (author) {
    query = query.eq('author_id', author);
  }

  // Apply sorting
  query = query.order(sortBy, { ascending: sortOrder === 'asc' });

  // Apply pagination
  const offset = (page - 1) * limit;
  query = query.range(offset, offset + limit - 1);

  const { data, error, count } = await query;

  if (error) {
    return {
      success: false,
      error: error.message,
    };
  }

  const totalPages = count ? Math.ceil(count / limit) : 0;

  return {
    success: true,
    data: data as PostWithRelations[],
    pagination: {
      page,
      limit,
      total: count || 0,
      totalPages,
      hasNext: page < totalPages,
      hasPrev: page > 1,
    },
  };
}

/**
 * Get a single post by ID or slug
 */
export async function getPost(idOrSlug: string): Promise<{ data?: PostWithRelations; error?: string }> {
  const supabase = createClient();

  const { data, error } = await supabase
    .from('posts')
    .select(`
      *,
      author:profiles!posts_author_id_fkey(id, full_name, email, avatar_url),
      category:categories!posts_category_id_fkey(id, name, slug),
      tags:post_tags!inner(tag:tags!inner(id, name, slug)),
      last_edited_by:profiles!posts_last_edited_by_id_fkey(id, full_name, email)
    `)
    .or(`id.eq.${idOrSlug}, slug.eq.${idOrSlug}`)
    .is('deleted_at', null)
    .single();

  if (error) {
    return { error: error.message };
  }

  return { data: data as PostWithRelations };
}

/**
 * Create a new post
 */
export async function createPost(postData: InsertPost): Promise<{ data?: Post; error?: string }> {
  const supabase = createClient();

  const { data, error } = await supabase
    .from('posts')
    .insert(postData)
    .select()
    .single();

  if (error) {
    return { error: error.message };
  }

  return { data };
}

/**
 * Update an existing post
 */
export async function updatePost(id: string, updates: Omit<UpdatePost, 'id'>): Promise<{ data?: Post; error?: string }> {
  const supabase = createClient();

  const { data, error } = await supabase
    .from('posts')
    .update({ ...updates, updated_at: new Date().toISOString() })
    .eq('id', id)
    .select()
    .single();

  if (error) {
    return { error: error.message };
  }

  return { data };
}

/**
 * Soft delete a post
 */
export async function deletePost(id: string): Promise<{ success: boolean; error?: string }> {
  const supabase = createClient();

  const { error } = await supabase
    .from('posts')
    .update({ deleted_at: new Date().toISOString() })
    .eq('id', id);

  if (error) {
    return { success: false, error: error.message };
  }

  return { success: true };
}

// =============================================================================
// PROFILE OPERATIONS
// =============================================================================

/**
 * Get user profile by ID
 */
export async function getProfile(id: string): Promise<{ data?: Profile; error?: string }> {
  const supabase = createClient();

  const { data, error } = await supabase
    .from('profiles')
    .select('*')
    .eq('id', id)
    .is('deleted_at', null)
    .single();

  if (error) {
    return { error: error.message };
  }

  return { data };
}

/**
 * Update user profile
 */
export async function updateProfile(
  id: string,
  updates: Partial<Pick<Profile, 'full_name' | 'avatar_url'>>
): Promise<{ data?: Profile; error?: string }> {
  const supabase = createClient();

  const { data, error } = await supabase
    .from('profiles')
    .update({ ...updates, updated_at: new Date().toISOString() })
    .eq('id', id)
    .select()
    .single();

  if (error) {
    return { error: error.message };
  }

  return { data };
}

// =============================================================================
// CATEGORY OPERATIONS
// =============================================================================

/**
 * Get all categories
 */
export async function getCategories(): Promise<{ data?: Category[]; error?: string }> {
  const supabase = createClient();

  const { data, error } = await supabase
    .from('categories')
    .select('*')
    .is('deleted_at', null)
    .order('name');

  if (error) {
    return { error: error.message };
  }

  return { data };
}

// =============================================================================
// TAG OPERATIONS
// =============================================================================

/**
 * Get all tags
 */
export async function getTags(): Promise<{ data?: Tag[]; error?: string }> {
  const supabase = createClient();

  const { data, error } = await supabase
    .from('tags')
    .select('*')
    .is('deleted_at', null)
    .order('name');

  if (error) {
    return { error: error.message };
  }

  return { data };
}

// =============================================================================
// UTILITY FUNCTIONS
// =============================================================================

/**
 * Generate a URL-friendly slug from a title
 */
export function generateSlug(title: string): string {
  return title
    .toLowerCase()
    .replace(/[^\w\s-]/g, '') // Remove special characters
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/-+/g, '-') // Replace multiple hyphens with single hyphen
    .trim();
}

/**
 * Check if a slug is unique
 */
export async function isSlugUnique(slug: string, table: 'posts' | 'pages' | 'categories' | 'tags', excludeId?: string): Promise<boolean> {
  const supabase = createClient();

  let query = supabase
    .from(table)
    .select('id')
    .eq('slug', slug)
    .is('deleted_at', null);

  if (excludeId) {
    query = query.neq('id', excludeId);
  }

  const { data, error } = await query;

  if (error) {
    console.error('Error checking slug uniqueness:', error);
    return false;
  }

  return data.length === 0;
}

/**
 * Get content status options for forms
 */
export function getContentStatusOptions(): Array<{ value: ContentStatus; label: string }> {
  return [
    { value: 'draft', label: 'Draft' },
    { value: 'pending_review', label: 'Pending Review' },
    { value: 'rejected', label: 'Rejected' },
    { value: 'published', label: 'Published' },
    { value: 'archived', label: 'Archived' },
  ];
}

/**
 * Get user role options for forms
 */
export function getUserRoleOptions(): Array<{ value: UserRole; label: string }> {
  return [
    { value: 'admin', label: 'Administrator' },
    { value: 'publisher', label: 'Publisher' },
    { value: 'editor', label: 'Editor' },
    { value: 'member', label: 'Member' },
  ];
}
